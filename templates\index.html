<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文章撰写工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .news-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .news-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .topic-tag {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #e9ecef;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .loading {
            display: none;
        }
        .article-preview {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-newspaper"></i> AI文章撰写工具
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showSettings()">
                    <i class="bi bi-gear"></i> 设置
                </a>
                <a class="nav-link" href="#" onclick="showHelp()">
                    <i class="bi bi-question-circle"></i> 帮助
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 热点话题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-fire"></i> 热点话题</h5>
                    </div>
                    <div class="card-body">
                        <div id="trending-topics">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能选项卡 -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="news-tab" data-bs-toggle="tab" data-bs-target="#news-panel" type="button" role="tab">
                            <i class="bi bi-newspaper"></i> 新闻撰写
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="articles-tab" data-bs-toggle="tab" data-bs-target="#articles-panel" type="button" role="tab">
                            <i class="bi bi-folder"></i> 文章管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics-panel" type="button" role="tab">
                            <i class="bi bi-graph-up"></i> 数据分析
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <div class="tab-content" id="mainTabContent">
            <!-- 新闻撰写面板 -->
            <div class="tab-pane fade show active" id="news-panel" role="tabpanel">
                <div class="row">
                    <!-- 新闻列表 -->
                    <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 热点新闻</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshNews()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="news-list">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文章撰写 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-pencil-square"></i> 文章撰写</h5>
                    </div>
                    <div class="card-body">
                        <div id="article-form" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">选中新闻：</label>
                                <div id="selected-news" class="alert alert-info"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="article-type" class="form-label">文章类型：</label>
                                <select class="form-select" id="article-type">
                                    <option value="breaking_news">突发新闻</option>
                                    <option value="analysis">深度分析</option>
                                    <option value="feature">特稿报道</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="writing-style" class="form-label">写作风格：</label>
                                <select class="form-select" id="writing-style">
                                    <option value="professional">专业严谨</option>
                                    <option value="casual">轻松易读</option>
                                    <option value="academic">学术分析</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="article-length" class="form-label">文章长度：</label>
                                <select class="form-select" id="article-length">
                                    <option value="short">简短 (500-800字)</option>
                                    <option value="medium" selected>中等 (800-1500字)</option>
                                    <option value="long">详细 (1500-2500字)</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-quality-check" checked>
                                    <label class="form-check-label" for="enable-quality-check">
                                        启用质量检查
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="writeArticle()">
                                    <i class="bi bi-magic"></i> 开始撰写
                                </button>
                                <button class="btn btn-outline-secondary" onclick="previewArticle()" style="display: none;" id="preview-btn">
                                    <i class="bi bi-eye"></i> 预览文章
                                </button>
                            </div>
                        </div>
                        
                        <div id="no-selection" class="text-muted text-center">
                            <i class="bi bi-arrow-left"></i> 请先选择一条新闻
                        </div>
                        
                        <div id="article-loading" class="loading text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">正在撰写文章...</span>
                            </div>
                            <p class="mt-2">AI正在撰写文章，请稍候...</p>
                        </div>
                        
                        <div id="article-result" style="display: none;">
                            <h6>生成的文章：</h6>
                            <div id="article-content" class="article-preview"></div>
                            <div class="mt-3">
                                <button class="btn btn-success btn-sm" onclick="downloadArticle()">
                                    <i class="bi bi-download"></i> 下载文章
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="copyArticle()">
                                    <i class="bi bi-clipboard"></i> 复制内容
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 已生成文章列表 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-folder"></i> 已生成文章</h6>
                    </div>
                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                        <div id="articles-list">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文章管理面板 -->
            <div class="tab-pane fade" id="articles-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-folder"></i> 文章库</h5>
                                <div>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-2" id="article-filter">
                                        <option value="all">全部文章</option>
                                        <option value="breaking_news">突发新闻</option>
                                        <option value="analysis">深度分析</option>
                                        <option value="feature">特稿报道</option>
                                    </select>
                                    <button class="btn btn-sm btn-outline-primary" onclick="refreshArticlesList()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="articles-management-list">
                                    <div class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-info-circle"></i> 文章详情</h6>
                            </div>
                            <div class="card-body" id="article-details">
                                <p class="text-muted">选择一篇文章查看详情</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析面板 -->
            <div class="tab-pane fade" id="analytics-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-graph-up"></i> 撰写统计</h5>
                            </div>
                            <div class="card-body">
                                <div id="writing-stats">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h3 class="text-primary" id="total-articles">0</h3>
                                            <small class="text-muted">总文章数</small>
                                        </div>
                                        <div class="col-4">
                                            <h3 class="text-success" id="today-articles">0</h3>
                                            <small class="text-muted">今日撰写</small>
                                        </div>
                                        <div class="col-4">
                                            <h3 class="text-info" id="avg-quality">0.0</h3>
                                            <small class="text-muted">平均质量</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-pie-chart"></i> 文章类型分布</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="article-type-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-clock-history"></i> 最近活动</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-activity">
                                    <p class="text-muted">暂无活动记录</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-gear"></i> 系统设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="default-article-type" class="form-label">默认文章类型：</label>
                        <select class="form-select" id="default-article-type">
                            <option value="breaking_news">突发新闻</option>
                            <option value="analysis">深度分析</option>
                            <option value="feature">特稿报道</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="default-style" class="form-label">默认写作风格：</label>
                        <select class="form-select" id="default-style">
                            <option value="professional">专业严谨</option>
                            <option value="casual">轻松易读</option>
                            <option value="academic">学术分析</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto-save" checked>
                            <label class="form-check-label" for="auto-save">
                                自动保存文章
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-question-circle"></i> 使用帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>如何使用AI文章撰写工具：</h6>
                    <ol>
                        <li><strong>选择新闻：</strong>在左侧新闻列表中点击选择要撰写的新闻</li>
                        <li><strong>配置参数：</strong>选择文章类型、写作风格和长度</li>
                        <li><strong>开始撰写：</strong>点击"开始撰写"按钮，AI将自动生成文章</li>
                        <li><strong>质量检查：</strong>系统会自动评估文章质量并提供改进建议</li>
                        <li><strong>保存下载：</strong>满意后可以保存或下载文章</li>
                    </ol>
                    <h6>功能说明：</h6>
                    <ul>
                        <li><strong>新闻撰写：</strong>基于热点新闻生成原创文章</li>
                        <li><strong>文章管理：</strong>查看、编辑和管理已生成的文章</li>
                        <li><strong>数据分析：</strong>查看撰写统计和质量分析</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">了解了</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedNewsId = null;
        let currentArticleFilename = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTrendingTopics();
            loadNews();
            loadArticlesList();
            loadSettings();
            loadAnalytics();

            // 初始化选项卡切换事件
            document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(event) {
                    const target = event.target.getAttribute('data-bs-target');
                    if (target === '#articles-panel') {
                        loadArticlesManagement();
                    } else if (target === '#analytics-panel') {
                        loadAnalytics();
                    }
                });
            });
        });

        // 加载热点话题
        function loadTrendingTopics() {
            fetch('/api/topics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTrendingTopics(data.data);
                    } else {
                        document.getElementById('trending-topics').innerHTML = 
                            '<div class="text-danger">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('trending-topics').innerHTML = 
                        '<div class="text-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示热点话题
        function displayTrendingTopics(topics) {
            const container = document.getElementById('trending-topics');
            if (topics.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无热点话题</div>';
                return;
            }

            const html = topics.map(topic => 
                `<span class="topic-tag">${topic.word} (${topic.count})</span>`
            ).join('');
            
            container.innerHTML = html;
        }

        // 加载新闻列表
        function loadNews() {
            fetch('/api/news?limit=20')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayNews(data.data);
                    } else {
                        document.getElementById('news-list').innerHTML = 
                            '<div class="text-danger">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('news-list').innerHTML = 
                        '<div class="text-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示新闻列表
        function displayNews(newsList) {
            const container = document.getElementById('news-list');
            if (newsList.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无新闻</div>';
                return;
            }

            const html = newsList.map(news => `
                <div class="news-card card mb-2" onclick="selectNews(${news.id}, '${news.title.replace(/'/g, "\\'")}')">
                    <div class="card-body p-3">
                        <h6 class="card-title">${news.title}</h6>
                        <p class="card-text text-muted small">${news.summary.substring(0, 100)}...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${news.source}</small>
                            <small class="text-muted">${new Date(news.publish_time).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 选择新闻
        function selectNews(newsId, title) {
            selectedNewsId = newsId;
            
            // 更新选中状态
            document.querySelectorAll('.news-card').forEach(card => {
                card.classList.remove('border-primary');
            });
            event.currentTarget.classList.add('border-primary');
            
            // 显示选中的新闻
            document.getElementById('selected-news').textContent = title;
            document.getElementById('article-form').style.display = 'block';
            document.getElementById('no-selection').style.display = 'none';
        }

        // 撰写文章
        function writeArticle() {
            if (!selectedNewsId) {
                alert('请先选择一条新闻');
                return;
            }

            const articleType = document.getElementById('article-type').value;
            const writingStyle = document.getElementById('writing-style').value;

            // 显示加载状态
            document.getElementById('article-loading').style.display = 'block';
            document.getElementById('article-result').style.display = 'none';

            fetch('/api/write_article', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    news_id: selectedNewsId,
                    article_type: articleType,
                    style: writingStyle
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('article-loading').style.display = 'none';
                
                if (data.success) {
                    document.getElementById('article-content').textContent = data.data.article;
                    document.getElementById('article-result').style.display = 'block';
                    currentArticleFilename = data.data.filename;
                    
                    // 刷新文章列表
                    loadArticlesList();
                } else {
                    alert('文章撰写失败: ' + data.error);
                }
            })
            .catch(error => {
                document.getElementById('article-loading').style.display = 'none';
                alert('网络错误: ' + error.message);
            });
        }

        // 下载文章
        function downloadArticle() {
            if (currentArticleFilename) {
                const filename = currentArticleFilename.split('/').pop();
                window.open('/download/' + filename, '_blank');
            }
        }

        // 复制文章内容
        function copyArticle() {
            const content = document.getElementById('article-content').textContent;
            navigator.clipboard.writeText(content).then(() => {
                alert('文章内容已复制到剪贴板');
            });
        }

        // 刷新新闻
        function refreshNews() {
            loadTrendingTopics();
            loadNews();
        }

        // 加载已生成文章列表
        function loadArticlesList() {
            fetch('/api/articles')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayArticlesList(data.data);
                    } else {
                        document.getElementById('articles-list').innerHTML = 
                            '<div class="text-danger">加载失败: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('articles-list').innerHTML = 
                        '<div class="text-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示文章列表
        function displayArticlesList(articles) {
            const container = document.getElementById('articles-list');
            if (articles.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无已生成文章</div>';
                return;
            }

            const html = articles.map(article => `
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div>
                        <div class="fw-bold">${article.title}</div>
                        <small class="text-muted">${new Date(article.created_time).toLocaleString()}</small>
                    </div>
                    <a href="/download/${article.filename}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-download"></i>
                    </a>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 设置相关函数
        function showSettings() {
            const modal = new bootstrap.Modal(document.getElementById('settingsModal'));
            modal.show();
        }

        function showHelp() {
            const modal = new bootstrap.Modal(document.getElementById('helpModal'));
            modal.show();
        }

        function loadSettings() {
            // 从localStorage加载设置
            const settings = JSON.parse(localStorage.getItem('articleSettings') || '{}');

            if (settings.defaultArticleType) {
                document.getElementById('default-article-type').value = settings.defaultArticleType;
                document.getElementById('article-type').value = settings.defaultArticleType;
            }

            if (settings.defaultStyle) {
                document.getElementById('default-style').value = settings.defaultStyle;
                document.getElementById('writing-style').value = settings.defaultStyle;
            }

            if (settings.autoSave !== undefined) {
                document.getElementById('auto-save').checked = settings.autoSave;
            }
        }

        function saveSettings() {
            const settings = {
                defaultArticleType: document.getElementById('default-article-type').value,
                defaultStyle: document.getElementById('default-style').value,
                autoSave: document.getElementById('auto-save').checked
            };

            localStorage.setItem('articleSettings', JSON.stringify(settings));

            // 应用设置到当前表单
            document.getElementById('article-type').value = settings.defaultArticleType;
            document.getElementById('writing-style').value = settings.defaultStyle;

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
            modal.hide();

            alert('设置已保存');
        }

        // 文章管理相关函数
        function loadArticlesManagement() {
            fetch('/api/articles')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayArticlesManagement(data.data);
                    }
                })
                .catch(error => {
                    console.error('加载文章管理失败:', error);
                });
        }

        function displayArticlesManagement(articles) {
            const container = document.getElementById('articles-management-list');

            if (articles.length === 0) {
                container.innerHTML = '<div class="text-muted">暂无文章</div>';
                return;
            }

            const html = articles.map(article => `
                <div class="article-item border-bottom py-3" onclick="showArticleDetails('${article.filename}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${article.title}</h6>
                            <small class="text-muted">
                                创建时间: ${new Date(article.created_time).toLocaleString()}
                                | 大小: ${(article.size / 1024).toFixed(1)} KB
                            </small>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="event.stopPropagation(); editArticle('${article.filename}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="event.stopPropagation(); downloadArticle('${article.filename}')">
                                <i class="bi bi-download"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="event.stopPropagation(); deleteArticle('${article.filename}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        function showArticleDetails(filename) {
            fetch(`/api/articles/${filename}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const content = data.data.content;
                        const wordCount = content.length;
                        const lines = content.split('\n').length;

                        document.getElementById('article-details').innerHTML = `
                            <h6>${filename}</h6>
                            <p><strong>字数:</strong> ${wordCount}</p>
                            <p><strong>行数:</strong> ${lines}</p>
                            <div class="mt-3">
                                <h6>内容预览:</h6>
                                <div class="border p-2 small" style="max-height: 200px; overflow-y: auto;">
                                    ${content.substring(0, 300)}...
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载文章详情失败:', error);
                });
        }

        function refreshArticlesList() {
            loadArticlesManagement();
        }

        // 数据分析相关函数
        function loadAnalytics() {
            // 模拟数据分析
            updateWritingStats();
            updateArticleTypeChart();
            updateRecentActivity();
        }

        function updateWritingStats() {
            fetch('/api/articles')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const articles = data.data;
                        const today = new Date().toDateString();
                        const todayArticles = articles.filter(article =>
                            new Date(article.created_time).toDateString() === today
                        );

                        document.getElementById('total-articles').textContent = articles.length;
                        document.getElementById('today-articles').textContent = todayArticles.length;
                        document.getElementById('avg-quality').textContent = '8.5'; // 模拟数据
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                });
        }

        function updateArticleTypeChart() {
            // 这里可以集成Chart.js来显示图表
            const canvas = document.getElementById('article-type-chart');
            const ctx = canvas.getContext('2d');

            // 简单的饼图模拟
            ctx.fillStyle = '#007bff';
            ctx.fillRect(10, 10, 100, 20);
            ctx.fillStyle = '#28a745';
            ctx.fillRect(10, 40, 80, 20);
            ctx.fillStyle = '#ffc107';
            ctx.fillRect(10, 70, 60, 20);

            ctx.fillStyle = '#000';
            ctx.font = '12px Arial';
            ctx.fillText('突发新闻 (45%)', 120, 25);
            ctx.fillText('深度分析 (35%)', 120, 55);
            ctx.fillText('特稿报道 (20%)', 120, 85);
        }

        function updateRecentActivity() {
            const activities = [
                { time: '2分钟前', action: '生成了一篇突发新闻文章' },
                { time: '15分钟前', action: '完成了深度分析文章' },
                { time: '1小时前', action: '更新了系统设置' }
            ];

            const html = activities.map(activity => `
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span>${activity.action}</span>
                    <small class="text-muted">${activity.time}</small>
                </div>
            `).join('');

            document.getElementById('recent-activity').innerHTML = html;
        }
    </script>
</body>
</html>
