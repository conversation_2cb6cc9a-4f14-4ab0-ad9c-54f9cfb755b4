#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务调试脚本
"""

import os
import sys

def debug_web_service():
    """调试Web服务"""
    print("=== Web服务调试 ===")
    
    # 检查模板文件
    template_path = "templates/index.html"
    if os.path.exists(template_path):
        print(f"✅ 模板文件存在: {template_path}")
    else:
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    # 检查源代码模块
    try:
        from src.web_interface import create_app
        print("✅ Web界面模块导入成功")
    except Exception as e:
        print(f"❌ Web界面模块导入失败: {e}")
        return False
    
    # 尝试创建应用
    try:
        app = create_app()
        print("✅ Flask应用创建成功")
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        return False
    
    # 启动服务
    try:
        print("🚀 启动Web服务...")
        print("📍 访问地址: http://localhost:5000")
        
        app.run(
            host='127.0.0.1',  # 只监听本地
            port=5000,
            debug=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ Web服务启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    debug_web_service()
