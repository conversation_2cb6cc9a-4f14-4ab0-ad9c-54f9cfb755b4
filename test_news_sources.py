#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻源测试脚本
测试各个新闻源的可用性和质量
"""

import sys
import time
from datetime import datetime

def test_news_sources():
    """测试新闻源"""
    try:
        print("=== 测试新闻源 ===\n")
        
        from src.news_analyzer import NewsAnalyzer
        analyzer = NewsAnalyzer()
        
        print("正在获取热点新闻...")
        start_time = time.time()
        news_list = analyzer.get_trending_news(limit=10)
        end_time = time.time()
        
        print(f"获取耗时: {end_time - start_time:.2f} 秒")
        
        if news_list:
            print(f"✅ 成功获取 {len(news_list)} 条新闻")
            
            for i, news in enumerate(news_list[:3], 1):
                print(f"\n{i}. 标题: {news['title']}")
                print(f"   来源: {news['source']}")
                print(f"   时间: {news['publish_time']}")
                print(f"   摘要: {news['summary'][:100]}...")
                print(f"   内容长度: {len(news['content'])} 字符")
                print(f"   链接: {news['link']}")
        else:
            print("❌ 未获取到新闻")
            return False
        
        # 测试热点话题分析
        print("\n=== 测试热点话题分析 ===")
        topics = analyzer.analyze_trending_topics(news_list)
        
        if topics:
            print("✅ 热点话题:")
            for word, count in topics[:10]:
                print(f"   {word}: {count}")
        else:
            print("❌ 未分析出热点话题")
        
        return True
        
    except Exception as e:
        print(f"❌ 新闻源测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_sources():
    """测试各个新闻源"""
    try:
        print("\n=== 测试各个新闻源 ===\n")
        
        from src.news_analyzer import NewsAnalyzer
        analyzer = NewsAnalyzer()
        
        results = {}
        
        for source in analyzer.news_sources:
            print(f"测试 {source['name']}...")
            
            try:
                start_time = time.time()
                
                if source['type'] == 'rss':
                    news_items = analyzer._parse_rss_feed(source['rss_url'], source['name'])
                elif source['type'] == 'api':
                    news_items = analyzer._parse_api_feed(source['api_url'], source['name'])
                else:
                    news_items = []
                
                end_time = time.time()
                
                if news_items:
                    print(f"✅ {source['name']}: 获取到 {len(news_items)} 条新闻 (耗时: {end_time - start_time:.2f}s)")
                    results[source['name']] = {
                        'success': True,
                        'count': len(news_items),
                        'time': end_time - start_time,
                        'sample': news_items[0] if news_items else None
                    }
                else:
                    print(f"⚠️  {source['name']}: 未获取到新闻 (耗时: {end_time - start_time:.2f}s)")
                    results[source['name']] = {
                        'success': False,
                        'count': 0,
                        'time': end_time - start_time,
                        'sample': None
                    }
                    
            except Exception as e:
                print(f"❌ {source['name']}: 失败 - {e}")
                results[source['name']] = {
                    'success': False,
                    'count': 0,
                    'time': 0,
                    'error': str(e)
                }
        
        # 输出汇总结果
        print("\n=== 新闻源测试汇总 ===")
        successful_sources = 0
        total_news = 0
        
        for name, result in results.items():
            if result['success']:
                successful_sources += 1
                total_news += result['count']
                print(f"✅ {name}: {result['count']} 条新闻")
            else:
                error_msg = result.get('error', '无新闻')
                print(f"❌ {name}: {error_msg}")
        
        print(f"\n成功率: {successful_sources}/{len(results)} ({successful_sources/len(results)*100:.1f}%)")
        print(f"总新闻数: {total_news}")
        
        return successful_sources > 0
        
    except Exception as e:
        print(f"❌ 个别源测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_news_quality():
    """测试新闻质量"""
    try:
        print("\n=== 测试新闻质量 ===\n")
        
        from src.news_analyzer import NewsAnalyzer
        analyzer = NewsAnalyzer()
        
        news_list = analyzer.get_trending_news(limit=5)
        
        if not news_list:
            print("❌ 无新闻可测试")
            return False
        
        quality_issues = []
        
        for i, news in enumerate(news_list, 1):
            print(f"检查新闻 {i}: {news['title'][:50]}...")
            
            # 检查必要字段
            required_fields = ['id', 'title', 'content', 'summary', 'source', 'link', 'publish_time']
            missing_fields = [field for field in required_fields if not news.get(field)]
            
            if missing_fields:
                quality_issues.append(f"新闻 {i} 缺少字段: {missing_fields}")
            
            # 检查内容长度
            if len(news.get('content', '')) < 50:
                quality_issues.append(f"新闻 {i} 内容过短: {len(news.get('content', ''))} 字符")
            
            # 检查标题长度
            if len(news.get('title', '')) < 10:
                quality_issues.append(f"新闻 {i} 标题过短")
            
            # 检查时间
            if isinstance(news.get('publish_time'), datetime):
                time_diff = datetime.now() - news['publish_time']
                if time_diff.days > 7:
                    quality_issues.append(f"新闻 {i} 时间过旧: {time_diff.days} 天前")
        
        if quality_issues:
            print("⚠️  发现质量问题:")
            for issue in quality_issues:
                print(f"   - {issue}")
        else:
            print("✅ 新闻质量良好")
        
        return len(quality_issues) == 0
        
    except Exception as e:
        print(f"❌ 新闻质量测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 新闻源综合测试 ===\n")
    
    # 测试整体新闻获取
    overall_success = test_news_sources()
    
    # 测试各个新闻源
    individual_success = test_individual_sources()
    
    # 测试新闻质量
    quality_success = test_news_quality()
    
    print("\n=== 测试结果汇总 ===")
    print(f"整体新闻获取: {'✅ 通过' if overall_success else '❌ 失败'}")
    print(f"个别新闻源: {'✅ 通过' if individual_success else '❌ 失败'}")
    print(f"新闻质量: {'✅ 通过' if quality_success else '❌ 失败'}")
    
    if overall_success and individual_success:
        print("\n✅ 新闻源功能基本正常")
        if not quality_success:
            print("⚠️  建议改进新闻质量")
        return True
    else:
        print("\n❌ 新闻源存在严重问题，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
