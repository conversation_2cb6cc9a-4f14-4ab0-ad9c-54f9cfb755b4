#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的Web服务测试脚本
"""

import os
import sys
import traceback

def test_web_components():
    """测试Web组件"""
    print("=== 详细Web组件测试 ===\n")
    
    # 1. 检查模板文件
    print("1. 检查模板文件...")
    template_path = "templates/index.html"
    if os.path.exists(template_path):
        print(f"✅ 模板文件存在: {template_path}")
        # 检查文件大小
        size = os.path.getsize(template_path)
        print(f"   文件大小: {size} 字节")
    else:
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    # 2. 测试模块导入
    print("\n2. 测试模块导入...")
    try:
        print("   导入 Flask...")
        from flask import Flask
        print("   ✅ Flask导入成功")
        
        print("   导入 NewsAnalyzer...")
        from src.news_analyzer import NewsAnalyzer
        print("   ✅ NewsAnalyzer导入成功")
        
        print("   导入 ArticleWriter...")
        from src.article_writer import ArticleWriter
        print("   ✅ ArticleWriter导入成功")
        
        print("   导入 create_app...")
        from src.web_interface import create_app
        print("   ✅ create_app导入成功")
        
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 3. 测试组件初始化
    print("\n3. 测试组件初始化...")
    try:
        print("   初始化 NewsAnalyzer...")
        analyzer = NewsAnalyzer()
        print("   ✅ NewsAnalyzer初始化成功")
        
        print("   初始化 ArticleWriter...")
        writer = ArticleWriter()
        print("   ✅ ArticleWriter初始化成功")
        
    except Exception as e:
        print(f"   ❌ 组件初始化失败: {e}")
        traceback.print_exc()
        return False
    
    # 4. 测试Flask应用创建
    print("\n4. 测试Flask应用创建...")
    try:
        app = create_app()
        print("   ✅ Flask应用创建成功")
        
        # 测试路由
        with app.test_client() as client:
            print("   测试主页路由...")
            response = client.get('/')
            print(f"   主页响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 主页路由正常")
            else:
                print(f"   ❌ 主页路由异常: {response.status_code}")
                print(f"   响应内容: {response.get_data(as_text=True)[:200]}...")
        
    except Exception as e:
        print(f"   ❌ Flask应用测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 5. 测试API端点
    print("\n5. 测试API端点...")
    try:
        with app.test_client() as client:
            # 测试新闻API
            print("   测试新闻API...")
            response = client.get('/api/news?limit=5')
            print(f"   新闻API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 新闻API正常")
            else:
                print(f"   ❌ 新闻API异常: {response.status_code}")
                print(f"   错误内容: {response.get_data(as_text=True)[:200]}...")
            
            # 测试文章列表API
            print("   测试文章列表API...")
            response = client.get('/api/articles')
            print(f"   文章列表API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 文章列表API正常")
            else:
                print(f"   ❌ 文章列表API异常: {response.status_code}")
        
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n✅ 所有Web组件测试通过！")
    return True

def start_web_service():
    """启动Web服务"""
    print("\n=== 启动Web服务 ===")
    
    try:
        from src.web_interface import create_app
        app = create_app()
        
        print("🚀 启动Web服务...")
        print("📍 访问地址: http://localhost:5000")
        print("💡 按 Ctrl+C 停止服务")
        
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ Web服务启动失败: {e}")
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    # 先测试组件
    if test_web_components():
        # 如果测试通过，启动服务
        start_web_service()
    else:
        print("❌ Web组件测试失败，无法启动服务")
        sys.exit(1)

if __name__ == "__main__":
    main()
