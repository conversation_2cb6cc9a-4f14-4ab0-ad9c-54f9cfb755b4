#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI文章撰写工具 - 测试包

这个包包含了项目的所有测试文件，用于验证系统功能的正确性和性能。

测试文件说明:
- test_basic.py: 基础功能测试
- test_comprehensive.py: 综合功能测试
- test_performance.py: 性能测试
- test_news.py: 新闻获取测试

运行测试:
    # 运行所有测试
    python -m pytest tests/
    
    # 运行特定测试
    python tests/test_basic.py
    python tests/test_comprehensive.py
    python tests/test_performance.py
    python tests/test_news.py
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__email__ = "<EMAIL>"
