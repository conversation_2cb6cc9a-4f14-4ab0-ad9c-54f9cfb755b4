#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文章数据结构
"""

from src.web_interface import create_app

def check_article_structure():
    """检查文章数据结构"""
    print("=== 检查文章数据结构 ===")
    
    app = create_app()
    
    with app.test_client() as client:
        response = client.get('/api/articles')
        
        if response.status_code == 200:
            data = response.get_json()
            articles = data['data']
            print(f"文章数量: {len(articles)}")
            print(f"数据源: {data.get('source', 'unknown')}")
            
            if articles:
                print("\n第一篇文章的结构:")
                first_article = articles[0]
                for key, value in first_article.items():
                    print(f"  {key}: {type(value).__name__} = {str(value)[:100]}...")
            else:
                print("没有文章数据")
        else:
            print(f"获取失败: {response.status_code}")

if __name__ == "__main__":
    check_article_structure()
