# AI文章撰写工具 - Git忽略文件

# =============================================================================
# Python相关
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# 项目特定文件
# =============================================================================
# 数据库文件
*.db
*.sqlite
*.sqlite3
articles.db*
test_articles.db*

# 日志文件
logs/
*.log
*.log.*

# 生成的文章文件（可选，根据需要调整）
articles/*.md
articles/*.txt
articles/*.pdf

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 配置文件（包含敏感信息）
.env
config.ini
settings.json

# 缓存文件
.cache/
cache/
*.cache

# 备份文件
*.bak
*.backup
*~

# =============================================================================
# 开发工具相关
# =============================================================================
# VS Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统相关
# =============================================================================
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 部署相关
# =============================================================================
# Docker
.dockerignore
Dockerfile
docker-compose.yml
docker-compose.override.yml

# Kubernetes
*.yaml
*.yml
k8s/

# 部署脚本
deploy/
deployment/
scripts/deploy*

# SSL证书
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# =============================================================================
# 其他
# =============================================================================
# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 媒体文件
*.jpg
*.jpeg
*.png
*.gif
*.mp4
*.avi
*.mov

# 文档文件（如果不需要版本控制）
*.pdf
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx

# 测试输出
test-results/
test-reports/
coverage-reports/

# 性能分析文件
*.prof
*.profile

# 锁文件（根据项目需要调整）
# poetry.lock
# Pipfile.lock
# yarn.lock
# package-lock.json
