#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库状态测试脚本
"""

from src.database import get_database_manager

def test_database_status():
    """测试数据库状态"""
    print("=== 数据库状态测试 ===")
    
    try:
        db = get_database_manager()
        print(f"数据库可用: {db.available}")
        
        if db.available:
            stats = db.get_statistics()
            print("\n数据库统计:")
            print(f"总文章数: {stats['total_articles']}")
            print(f"今日文章: {stats['today_articles']}")
            print(f"平均质量: {stats['avg_quality_score']:.2f}")
            print(f"质量等级分布: {stats['quality_grades']}")
            
            # 获取最近的文章
            recent_articles = db.get_articles(limit=5)
            print(f"\n最近的 {len(recent_articles)} 篇文章:")
            for i, article in enumerate(recent_articles, 1):
                print(f"{i}. {article['title'][:50]}...")
                print(f"   创建时间: {article['created_time']}")
                print(f"   质量评分: {article['quality_score']:.2f}")
                print()
        
        print("✅ 数据库测试完成")
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_status()
