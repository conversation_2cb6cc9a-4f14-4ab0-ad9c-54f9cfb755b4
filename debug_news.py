#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试新闻获取问题
"""

import time
from src.news_analyzer import NewsAnalyzer

def debug_news_loading():
    """调试新闻加载问题"""
    print("=== 调试新闻加载问题 ===")
    
    try:
        print("1. 创建新闻分析器...")
        analyzer = NewsAnalyzer()
        print(f"   ✅ 分析器创建成功，配置了 {len(analyzer.news_sources)} 个新闻源")
        
        print("2. 测试新闻源...")
        for i, source in enumerate(analyzer.news_sources, 1):
            url = source.get('rss_url') or source.get('api_url', 'N/A')
            print(f"   {i}. {source['name']}: {url} ({source['type']})")
        
        print("3. 开始获取新闻...")
        start_time = time.time()
        
        try:
            news_list = analyzer.get_trending_news(limit=5)
            end_time = time.time()
            
            print(f"   ✅ 获取完成，耗时 {end_time - start_time:.2f} 秒")
            print(f"   📰 获取到 {len(news_list)} 条新闻")
            
            if news_list:
                print("   前3条新闻:")
                for i, news in enumerate(news_list[:3], 1):
                    print(f"     {i}. {news['title'][:50]}...")
                    print(f"        来源: {news['source']}")
                    print(f"        ID: {news['id']}")
            else:
                print("   ⚠️  没有获取到新闻")
                
        except Exception as e:
            print(f"   ❌ 新闻获取失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("4. 测试热点话题分析...")
        try:
            if news_list:
                topics = analyzer.analyze_trending_topics(news_list)
                print(f"   ✅ 分析完成，获取到 {len(topics)} 个热点话题")
                print("   前5个话题:")
                for word, count in topics[:5]:
                    print(f"     - {word}: {count}")
            else:
                print("   ⚠️  没有新闻数据，跳过话题分析")
        except Exception as e:
            print(f"   ❌ 话题分析失败: {e}")
        
        print("5. 测试API响应...")
        from src.web_interface import create_app
        app = create_app()
        
        with app.test_client() as client:
            print("   测试 /api/news...")
            response = client.get('/api/news?limit=3')
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"   返回数据: {data['count']} 条新闻")
            else:
                print(f"   错误: {response.get_data(as_text=True)}")
            
            print("   测试 /api/topics...")
            response = client.get('/api/topics')
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"   返回数据: {len(data['data'])} 个话题")
            else:
                print(f"   错误: {response.get_data(as_text=True)}")
        
        print("\n✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_news_loading()
