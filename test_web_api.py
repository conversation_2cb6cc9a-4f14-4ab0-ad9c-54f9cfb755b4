#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web API测试脚本
"""

from src.web_interface import create_app
import json

def test_web_apis():
    """测试Web API"""
    print("=== Web API测试 ===")
    
    app = create_app()
    
    with app.test_client() as client:
        print("1. 测试主页...")
        response = client.get('/')
        print(f"   主页状态: {response.status_code}")
        
        print("2. 测试新闻API...")
        response = client.get('/api/news?limit=5')
        print(f"   新闻API状态: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"   获取新闻数: {data['count']}")
        
        print("3. 测试文章列表API...")
        response = client.get('/api/articles')
        print(f"   文章列表状态: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"   文章数量: {len(data['data'])}")
            print(f"   数据源: {data.get('source', 'unknown')}")
        
        print("4. 测试热点话题API...")
        response = client.get('/api/topics')
        print(f"   话题API状态: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"   话题数量: {len(data['data'])}")
        
        print("5. 测试统计API...")
        response = client.get('/api/analytics/stats')
        print(f"   统计API状态: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            stats = data['data']
            print(f"   总文章数: {stats['total_articles']}")
            print(f"   今日文章: {stats['today_articles']}")
        
        print("6. 测试文章撰写API...")
        # 先获取一个新闻ID
        news_response = client.get('/api/news?limit=1')
        if news_response.status_code == 200:
            news_data = news_response.get_json()
            if news_data['count'] > 0:
                news_id = news_data['data'][0]['id']
                
                write_response = client.post('/api/write_article', 
                    json={
                        'news_id': news_id,
                        'article_type': 'breaking_news',
                        'style': 'professional'
                    })
                print(f"   文章撰写状态: {write_response.status_code}")
                if write_response.status_code == 200:
                    result = write_response.get_json()
                    print(f"   文章生成成功: {len(result['data']['article'])} 字符")
                else:
                    result = write_response.get_json()
                    print(f"   文章撰写失败: {result.get('error', 'Unknown error')}")
            else:
                print("   没有可用的新闻进行测试")
        
        print("\n✅ Web API测试完成")

if __name__ == "__main__":
    test_web_apis()
