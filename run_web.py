#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务启动脚本
直接启动Web界面，无需用户交互
"""

from src.web_interface import create_app

def main():
    """启动Web服务"""
    print("=== AI文章撰写工具 Web服务 ===")
    print("正在启动Web服务...")
    
    try:
        app = create_app()
        print("✅ Web应用创建成功")
        print("🌐 服务地址: http://localhost:5000")
        print("🚀 正在启动服务器...")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,  # 生产模式
            use_reloader=False  # 避免重复启动
        )
        
    except Exception as e:
        print(f"❌ Web服务启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
