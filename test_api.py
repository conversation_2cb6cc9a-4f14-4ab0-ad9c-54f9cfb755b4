#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
"""

from src.web_interface import create_app

def test_write_article_api():
    """测试文章撰写API"""
    print("=== 测试文章撰写API ===")
    
    app = create_app()
    print("✅ Flask应用创建成功")
    
    with app.test_client() as client:
        # 测试POST请求
        response = client.post('/api/write_article', json={
            'news_id': '123',
            'article_type': 'breaking_news',
            'style': 'professional'
        })
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.get_json()}")
        
        if response.status_code == 200:
            print("✅ API测试成功")
        else:
            print("❌ API测试失败")

if __name__ == "__main__":
    test_write_article_api()
